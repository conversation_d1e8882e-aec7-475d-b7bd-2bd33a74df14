<?php
/**
 * component-category-slider.php (v3 - with custom swipe)
 *
 * Переиспользуемый компонент для отображения одной категории товаров.
 */

if (!isset($category) || !is_array($category)) { return; }
$images = $category['images'] ?? [];
$total_slides = count($images);
?>
<section class="category-section">
    <div class="container">
        <div class="category-section-header">
            <hgroup>
                <h2><?= htmlspecialchars($category['title']) ?></h2>
                <p><?= htmlspecialchars($category['description'] ?? '') ?></p>
            </hgroup>
            <div class="category-meta">
                <div class="price-tag">от <?= htmlspecialchars($category['price_from']) ?></div>
                <button class="outline" @click="$dispatch('open-modal', { title: 'Расчет стоимости: <?= htmlspecialchars($category['title']) ?>' })">
                    Рассчитать под ключ
                </button>
            </div>
        </div>

        <?php if ($total_slides > 0): ?>
        <div 
            class="slider-multi-item"
            x-data="carousel({ totalSlides: <?= $total_slides ?>, initialSlidesToShow: { desktop: 4, tablet: 2, mobile: 1 } })"
        >
            <div 
                class="slider-container"
                @mousedown.prevent="dragStart" @touchstart.prevent="dragStart"
                :class="{ 'is-dragging': isDragging }"
            >
                <div class="slider-wrapper-flex" :style="`transform: translateX(-${currentSlide * (100 / slidesToShow)}%); transition: ${isDragging ? 'none' : 'transform 0.4s cubic-bezier(0.4, 0, 0.2, 1)'};`">
                    <?php foreach ($images as $image): ?>
                        <div class="slide-item aspect-4-3">
                            <a 
                                href="<?= htmlspecialchars($image['full']) ?>" 
                                @click.prevent="if (!isDragging) $dispatch('open-lightbox', { src: '<?= htmlspecialchars($image['full']) ?>', alt: '<?= htmlspecialchars($image['alt']) ?>' })"
                                draggable="false"
                                class="slide-link"
                            >
                                <img src="<?= htmlspecialchars($image['thumb']) ?>" alt="<?= htmlspecialchars($image['alt']) ?>" loading="lazy" draggable="false">
                                <div class="zoom-icon"></div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>