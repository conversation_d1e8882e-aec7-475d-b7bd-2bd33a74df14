/*
 * style.css
 * Основные кастомные стили проекта (v2 - Refactored)
 */

/* --- 0. ГЛОБАЛЬНЫЕ СТИЛИ И ПЕРЕМЕННЫЕ --- */
:root {
    /* Здесь можно будет переопределить основные цвета для тем */
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}

/* Единая стилизация для всех кнопок */
[role="button"], button {
    --pico-font-weight: 600;
    --pico-border-radius: 8px;
    --pico-form-element-spacing-horizontal: 1.25rem;
}

/* Убираем стандартный outline, полагаемся на стили <PERSON> */
*:focus-visible {
    outline: none;
    box-shadow: var(--pico-form-element-focus-box-shadow);
}

/* --- HERO SECTION --- */
#hero {
    background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.6)), url('../images/hero-background.jpg');
    background-size: cover;
    background-position: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
    padding: 2rem 1rem;
    text-align: center;
}
#hero hgroup { margin-bottom: 2rem; }
#hero h1, #hero h2 { color: var(--pico-primary-inverse); }
#hero h1 { font-size: 2.5rem; }
#hero h2 { font-size: 1.2rem; font-weight: 400; max-width: 60ch; margin: 1rem auto 0; }
#hero button { --pico-font-size: 1.1rem; --pico-padding: 0.75rem 1.5rem; }
@media (min-width: 768px) {
    #hero h1 { font-size: 3.5rem; }
    #hero h2 { font-size: 1.5rem; }
}

/* --- HEADER --- */
body > header {
    --pico-nav-padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.8);
    border-bottom: 1px solid var(--pico-muted-border-color);
    transition: all 0.3s ease-in-out;
}
body > header.is-scrolled {
    --pico-nav-padding: 0.5rem 0;
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}
header .brand { font-size: 1.2rem; text-decoration: none; color: var(--pico-h1-color); }

/* --- MODAL --- */
#contact-modal .contact-options { display: grid; gap: 1rem; margin-top: 1.5rem; }
#contact-modal .contact-options a { text-align: center; }

/* --- CATEGORY SECTIONS --- */
.category-section {
    padding: 4rem 0;
    border-bottom: 1px solid var(--pico-muted-border-color);
}
.category-section:nth-of-type(2n) { background-color: var(--pico-muted-background-color); }
.category-section-header { display: flex; flex-direction: column; gap: 1.5rem; margin-bottom: 3rem; }
.category-section-header hgroup { margin-bottom: 0; }
.category-section-header h2 { margin-bottom: 0.5rem; }
.category-section-header p { font-size: 1.1rem; color: var(--pico-secondary); margin: 0; }
.category-meta {
    border: 1px solid var(--pico-muted-border-color);
    border-radius: var(--pico-border-radius);
    background-color: var(--pico-card-background-color);
    padding: 1.5rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
    margin: 0;
}
.category-meta .price-tag {
    background-color: transparent;
    color: var(--pico-h2-color);
    font-size: 1.75rem;
    font-weight: 700;
    padding: 0;
    border: none;
    line-height: 1.2;
}

@media (min-width: 768px) {
    .category-section-header { flex-direction: row; justify-content: space-between; align-items: flex-start; }
    .category-meta { min-width: 240px; }
}

/* --- GENERIC SLIDER STYLES --- */
.slider-container { overflow: hidden; cursor: grab; }
.slider-container:active { cursor: grabbing; }
.slider-wrapper-flex { display: flex; transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1); }
.slide-item { flex: 0 0 100%; padding: 0 0.5rem; box-sizing: border-box; }
.slide-item a { display: block; position: relative; border-radius: var(--pico-border-radius); overflow: hidden; background-color: var(--pico-card-background-color); }
.slide-item img { display: block; width: 100%; height: 100%; object-fit: cover; transition: transform 0.3s ease; }
.slide-item a:hover img { transform: scale(1.05); }
.slider-nav-external { display: flex; justify-content: center; gap: 1rem; margin-top: 1.5rem; }
.slider-nav-external button { width: 3rem; height: 3rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; }
.slider-nav-external button svg { width: 1.5rem; height: 1.5rem; }
.slider-nav-external button:disabled { opacity: 0.5; cursor: not-allowed; }

/* --- CATEGORY SLIDER SPECIFICS --- */
.category-section .slide-item { aspect-ratio: 4 / 3; }
.category-section .zoom-icon { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%) scale(0.8); width: 4rem; height: 4rem; background-color: rgba(0, 0, 0, 0.5); border-radius: 50%; backdrop-filter: blur(5px); display: flex; align-items: center; justify-content: center; transition: all 0.3s ease; opacity: 0; }
.category-section .zoom-icon::after { content: '⚲'; color: white; font-size: 2rem; }
.category-section .slide-item a:hover .zoom-icon { opacity: 1; transform: translate(-50%, -50%) scale(1); }
@media (min-width: 768px) { .category-section .slide-item { flex-basis: 50%; } }
@media (min-width: 992px) { .category-section .slide-item { flex-basis: 33.333%; } }
@media (min-width: 1200px) { .category-section .slide-item { flex-basis: 25%; } }

/* --- SOCIAL PROOF SECTION --- */
#social-proof { padding: 4rem 0; background-color: var(--pico-muted-background-color); }
#social-proof hgroup { text-align: center; margin-bottom: 3rem; }
#social-proof .social-gallery { margin-bottom: 3rem; }
#social-proof .social-gallery h3 { margin-bottom: 1.5rem; text-align: center; }
#social-proof .slide-item.aspect-16-9 { aspect-ratio: 16 / 9; }
#social-proof .slide-item.aspect-9-16 { aspect-ratio: 9 / 16; }
#social-proof .slide-item.aspect-1-1 { aspect-ratio: 1 / 1.1; }
#social-proof .play-icon { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 4rem; height: 4rem; background-color: rgba(0, 0, 0, 0.5); border-radius: 50%; backdrop-filter: blur(5px); display: flex; align-items: center; justify-content: center; transition: background-color 0.3s ease; }
#social-proof .play-icon::after { content: ''; display: block; width: 0; height: 0; border-top: 0.7rem solid transparent; border-bottom: 0.7rem solid transparent; border-left: 1.2rem solid white; margin-left: 0.3rem; }
#social-proof .slide-item a:hover .play-icon { background-color: rgba(var(--pico-primary-rgb), 0.8); }
@media (min-width: 768px) { #social-proof .slide-item { flex-basis: 50%; } }
@media (min-width: 1024px) { #social-proof .slide-item { flex-basis: 33.333%; } }

/* --- FAQ SECTION --- */
#faq { padding: 4rem 0; }
#faq hgroup { text-align: center; margin-bottom: 3rem; }
.faq-accordion details { border-bottom: 1px solid var(--pico-muted-border-color); margin-bottom: 0.5rem; }
.faq-accordion details[open] { padding-bottom: 1rem; }
.faq-accordion summary { font-weight: bold; cursor: pointer; padding: 1rem 0; list-style: none; }
.faq-accordion summary::-webkit-details-marker { display: none; }
.faq-accordion summary::after { content: '+'; float: right; font-size: 1.5rem; font-weight: 400; transition: transform 0.2s ease-in-out; }
.faq-accordion details[open] summary::after { transform: rotate(45deg); content: '×'; }
.faq-accordion details p { margin-top: 0.5rem; padding-left: 0.5rem; line-height: 1.7; }

/* --- MAP SECTION --- */
#map { position: relative; padding: 4rem 1rem; background-image: linear-gradient(to right, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.1) 60%), url('../images/map-background.jpg'); background-size: cover; background-position: center; }
.map-info { max-width: 450px; padding: 2rem; border-radius: var(--pico-border-radius); background-color: var(--pico-card-background-color); }
.map-info p { margin-bottom: 1.5rem; }
.map-info footer a { width: 100%; }
@media (max-width: 767px) {
    #map { background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../images/map-background.jpg'); display: flex; justify-content: center; align-items: center; }
    .map-info { width: 100%; text-align: center; }
}

/* --- FOOTER --- */
#main-footer { padding: 2rem 0; background-color: var(--pico-muted-background-color); border-top: 1px solid var(--pico-muted-border-color); }
.footer-content { display: flex; flex-direction: column; align-items: center; gap: 1.5rem; text-align: center; }
.footer-content small { line-height: 1.5; }
.social-links ul { display: flex; gap: 1.5rem; padding: 0; margin: 0; list-style: none; }
.social-links a { text-decoration: none; }
@media (min-width: 768px) { .footer-content { flex-direction: row; justify-content: space-between; } }

/* --- COOKIE BANNER --- */
#cookie-banner { position: fixed; bottom: 0; left: 0; right: 0; z-index: 2000; background-color: var(--pico-card-background-color); border-top: 1px solid var(--pico-muted-border-color); box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); padding: 1rem 0; }
#cookie-banner .container { display: flex; flex-direction: column; align-items: center; gap: 1rem; text-align: center; }
#cookie-banner p { margin: 0; font-size: 0.9rem; }
.cookie-buttons { display: flex; gap: 0.5rem; flex-shrink: 0; }
#cookie-banner .cookie-buttons button { --pico-font-size: 0.9rem; --pico-padding: 0.5rem 1rem; margin: 0; }
@media (min-width: 768px) { #cookie-banner .container { flex-direction: row; justify-content: space-between; text-align: left; } }

/* --- ALPINE.JS CLOAK --- */
[x-cloak] {display: none !important;}

/* --- LIGHTBOX --- */
.lightbox-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 9999; display: flex; align-items: center; justify-content: center; padding: 1rem; }
.lightbox-backdrop { position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); backdrop-filter: blur(8px); cursor: pointer; }
.lightbox-content { position: relative; z-index: 1; max-width: 90vw; max-height: 90vh; display: flex; flex-direction: column; gap: 1rem; }
.lightbox-content .close-button { position: absolute; top: -2.5rem; right: -1rem; width: 2.5rem; height: 2.5rem; padding: 0; border-radius: 50%; border: none; background: none; color: white; font-size: 2.5rem; line-height: 1; cursor: pointer; opacity: 0.8; }
.lightbox-content .close-button:hover { opacity: 1; }
.lightbox-content .image-container { display: flex; align-items: center; justify-content: center; }
.lightbox-content img { display: block; max-width: 100%; max-height: calc(90vh - 5rem); object-fit: contain; border-radius: var(--pico-border-radius); box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
.alt-text-container { text-align: center; color: var(--pico-primary-inverse); }
.alt-text-container p { margin: 0; }
 
/* Запрещаем выделение текста при свайпе */
.slider-container.is-dragging {cursor: grabbing;user-select: none;}

